import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { binaryPointsDb, walletBalanceDb } from '@/lib/database';
import { getTotalTeamCount, getDirectReferralCount } from '@/lib/referral';

// GET - Fetch detailed user information
export async function GET(
  request: NextRequest,
  context: { params: { userId: string } }
) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { userId } = context.params;

    // Fetch basic user information
    const userInfo = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        kycStatus: true,
        isActive: true,
        createdAt: true,
        referralId: true,
        referrerId: true,
      },
    });

    if (!userInfo) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Fetch wallet balance information
    const walletBalance = await walletBalanceDb.getOrCreate(userId);

    // Fetch mining units information
    const miningUnits = await prisma.miningUnit.findMany({
      where: { userId },
      select: {
        id: true,
        thsAmount: true,
        investmentAmount: true,
        totalEarned: true,
        status: true,
        expiryDate: true,
        createdAt: true,
      },
    });

    const miningStats = miningUnits.reduce(
      (acc, unit) => {
        const isActive = unit.status === 'ACTIVE' && unit.expiryDate > new Date();
        return {
          totalUnits: acc.totalUnits + 1,
          totalInvestment: acc.totalInvestment + unit.investmentAmount,
          totalTHS: acc.totalTHS + unit.thsAmount,
          activeTHS: acc.activeTHS + (isActive ? unit.thsAmount : 0),
        };
      },
      { totalUnits: 0, totalInvestment: 0, totalTHS: 0, activeTHS: 0 }
    );

    // Fetch binary points information
    const binaryPoints = await binaryPointsDb.findByUserId(userId);

    // Fetch referral statistics
    const directReferrals = await getDirectReferralCount(userId);
    const teamCounts = await getTotalTeamCount(userId);

    // Fetch recent activity (transactions)
    const recentTransactions = await prisma.transaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        type: true,
        amount: true,
        description: true,
        status: true,
        createdAt: true,
      },
    });

    // Transform recent activity
    const recentActivity = recentTransactions.map(transaction => ({
      type: transaction.type,
      description: transaction.description || `${transaction.type.replace('_', ' ')} - ${transaction.status}`,
      amount: transaction.amount,
      date: transaction.createdAt.toISOString(),
    }));

    // Calculate wallet totals
    const totalEarnings = await prisma.transaction.aggregate({
      where: {
        userId,
        type: {
          in: ['MINING_EARNINGS', 'DIRECT_REFERRAL_COMMISSION', 'BINARY_BONUS'],
        },
        status: 'COMPLETED',
      },
      _sum: { amount: true },
    });

    const totalDeposits = await prisma.transaction.aggregate({
      where: {
        userId,
        type: 'DEPOSIT',
        status: 'COMPLETED',
      },
      _sum: { amount: true },
    });

    const totalWithdrawals = await prisma.transaction.aggregate({
      where: {
        userId,
        type: 'WITHDRAWAL',
        status: 'COMPLETED',
      },
      _sum: { amount: true },
    });

    // Compile detailed user information
    const userDetails = {
      id: userInfo.id,
      email: userInfo.email,
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      role: userInfo.role,
      kycStatus: userInfo.kycStatus,
      isActive: userInfo.isActive,
      createdAt: userInfo.createdAt.toISOString(),
      referralId: userInfo.referralId,
      referrerId: userInfo.referrerId,
      walletBalance: {
        availableBalance: walletBalance.availableBalance,
        totalEarnings: totalEarnings._sum.amount || 0,
        totalDeposits: totalDeposits._sum.amount || 0,
        totalWithdrawals: totalWithdrawals._sum.amount || 0,
      },
      miningUnits: miningStats,
      binaryPoints: {
        leftPoints: binaryPoints?.leftPoints || 0,
        rightPoints: binaryPoints?.rightPoints || 0,
        totalMatched: binaryPoints?.totalMatched || 0,
        lastMatchDate: binaryPoints?.lastMatchDate?.toISOString(),
      },
      referralStats: {
        directReferrals,
        leftTeam: teamCounts.left,
        rightTeam: teamCounts.right,
        totalTeam: teamCounts.total,
      },
      recentActivity,
    };

    return NextResponse.json({
      success: true,
      data: userDetails,
    });

  } catch (error: any) {
    console.error('User details fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user details' },
      { status: 500 }
    );
  }
}
